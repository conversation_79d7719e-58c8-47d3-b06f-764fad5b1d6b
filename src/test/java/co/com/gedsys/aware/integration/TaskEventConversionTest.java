package co.com.gedsys.aware.integration;

import co.com.gedsys.commons.events.task.TaskAssignedEvent;
import co.com.gedsys.commons.events.task.TaskCompletedEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test para verificar que la conversión de mensajes a Java records funciona correctamente.
 * Este test no requiere Spring Boot completo, solo verifica la serialización/deserialización.
 */
public class TaskEventConversionTest {

    @Test
    public void testTaskAssignedEventSerialization() throws Exception {
        // Crear evento
        TaskAssignedEvent originalEvent = new TaskAssignedEvent(
            "task123",
            "formularioRedaccion", 
            "usuario1",
            "supervisor1",
            "Nueva tarea de redacción",
            "process456",
            LocalDateTime.now()
        );

        // Serializar con Jackson
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.findAndRegisterModules(); // Para LocalDateTime
        
        String json = objectMapper.writeValueAsString(originalEvent);
        assertNotNull(json);
        assertTrue(json.contains("task123"));
        assertTrue(json.contains("usuario1"));

        // Deserializar
        TaskAssignedEvent deserializedEvent = objectMapper.readValue(json, TaskAssignedEvent.class);
        
        // Verificar que los datos son correctos
        assertEquals(originalEvent.taskId(), deserializedEvent.taskId());
        assertEquals(originalEvent.assignedTo(), deserializedEvent.assignedTo());
        assertEquals(originalEvent.assignedBy(), deserializedEvent.assignedBy());
        assertEquals(originalEvent.title(), deserializedEvent.title());
    }

    @Test
    public void testTaskCompletedEventSerialization() throws Exception {
        // Crear evento
        TaskCompletedEvent originalEvent = new TaskCompletedEvent(
            "task456",
            "formularioAprobacion",
            "usuario2", 
            "Tarea de aprobación completada",
            "process789",
            LocalDateTime.now()
        );

        // Serializar con Jackson
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.findAndRegisterModules(); // Para LocalDateTime
        
        String json = objectMapper.writeValueAsString(originalEvent);
        assertNotNull(json);
        assertTrue(json.contains("task456"));
        assertTrue(json.contains("usuario2"));

        // Deserializar
        TaskCompletedEvent deserializedEvent = objectMapper.readValue(json, TaskCompletedEvent.class);
        
        // Verificar que los datos son correctos
        assertEquals(originalEvent.taskId(), deserializedEvent.taskId());
        assertEquals(originalEvent.completedBy(), deserializedEvent.completedBy());
        assertEquals(originalEvent.title(), deserializedEvent.title());
    }

    @Test
    public void testMessageConverterConfiguration() {
        // Verificar que el MessageConverter está configurado correctamente
        MessageConverter converter = new Jackson2JsonMessageConverter();
        assertNotNull(converter);
        
        // Este test verifica que la configuración básica funciona
        // En un entorno real, Spring inyectaría el converter configurado
        assertTrue(converter instanceof Jackson2JsonMessageConverter);
    }
}
