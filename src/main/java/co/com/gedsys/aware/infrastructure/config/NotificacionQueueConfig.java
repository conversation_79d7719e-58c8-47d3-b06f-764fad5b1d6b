package co.com.gedsys.aware.infrastructure.config;

import static co.com.gedsys.commons.constant.amqp.DeadLetterConstant.DLX;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import co.com.gedsys.commons.constant.amqp.QueueName;
import co.com.gedsys.commons.constant.amqp.RoutingKeyName;
import co.com.gedsys.commons.constant.amqp.TaskRoutingKeys;

/**
 * Configuración de colas y bindings para el manejo de notificaciones
 */
@Configuration
public class NotificacionQueueConfig {

    private static final String NOTIFICACIONES_DLQ = "notificaciones.dlq";
    private static final String NOTIFICACIONES_DLK = "notificaciones.dlk";

    @Bean
    Queue notificacionesDeadLetterQueue() {
        return QueueBuilder.durable(NOTIFICACIONES_DLQ)
                .withArgument("x-queue-type", "classic")
                .build();
    }

    @Bean
    @DependsOn({ "deadLetterExchange", "notificacionesDeadLetterQueue" })
    Binding notificacionesDeadLetterBinding(Queue notificacionesDeadLetterQueue,
            TopicExchange deadLetterExchange) {
        return BindingBuilder.bind(notificacionesDeadLetterQueue)
                .to(deadLetterExchange)
                .with(NOTIFICACIONES_DLK);
    }

    @Bean
    Queue notificacionNuevaQueue() {
        return QueueBuilder.durable(QueueName.NOTIFICACIONES)
                .withArgument("x-queue-type", "classic")
                .deadLetterExchange(DLX)
                .deadLetterRoutingKey(NOTIFICACIONES_DLK)
                .build();
    }

    @Bean
    @DependsOn({ "mainTopicExchange", "notificacionNuevaQueue" })
    Binding notificacionNuevaBinding(Queue notificacionNuevaQueue,
            TopicExchange mainTopicExchange) {
        return BindingBuilder.bind(notificacionNuevaQueue)
                .to(mainTopicExchange)
                .with(RoutingKeyName.NOTIFICACION_NUEVA);
    }

    @Bean
    @DependsOn({ "mainTopicExchange", "notificacionNuevaQueue" })
    Binding tareaAsignadaBinding(Queue notificacionNuevaQueue,
            TopicExchange mainTopicExchange) {
        return BindingBuilder.bind(notificacionNuevaQueue)
                .to(mainTopicExchange)
                .with(TaskRoutingKeys.TAREA_ASIGNADA);
    }

    @Bean
    @DependsOn({ "mainTopicExchange", "notificacionNuevaQueue" })
    Binding tareaCompletadaBinding(Queue notificacionNuevaQueue,
            TopicExchange mainTopicExchange) {
        return BindingBuilder.bind(notificacionNuevaQueue)
                .to(mainTopicExchange)
                .with(TaskRoutingKeys.TAREA_COMPLETADA);
    }

}
