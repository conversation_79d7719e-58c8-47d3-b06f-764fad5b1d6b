package co.com.gedsys.commons.events.task;

import java.time.LocalDateTime;

/**
 * Evento que se dispara cuando una tarea es completada por un usuario.
 * 
 * @param taskId ID único de la tarea
 * @param taskFormKey Clave del formulario de la tarea (ej: formularioRedaccion)
 * @param completedBy Usuario que completó la tarea
 * @param title Título descriptivo de la tarea
 * @param processId ID del proceso al que pertenece la tarea (opcional)
 * @param timestamp Momento en que se completó la tarea
 */
public record TaskCompletedEvent(
        String taskId,
        String taskFormKey,
        String completedBy,
        String title,
        String processId,
        LocalDateTime timestamp
) {
}
