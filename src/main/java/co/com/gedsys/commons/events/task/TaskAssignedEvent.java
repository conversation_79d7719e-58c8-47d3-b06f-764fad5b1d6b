package co.com.gedsys.commons.events.task;

import java.time.LocalDateTime;

/**
 * Evento que se dispara cuando una tarea es asignada a un usuario.
 * 
 * @param taskId ID único de la tarea
 * @param taskFormKey Clave del formulario de la tarea (ej: formularioRedaccion)
 * @param assignedTo Usuario al que se asigna la tarea
 * @param assignedBy Usuario que asigna la tarea
 * @param title Título descriptivo de la tarea
 * @param processId ID del proceso al que pertenece la tarea (opcional)
 * @param timestamp Momento en que se asignó la tarea
 */
public record TaskAssignedEvent(
        String taskId,
        String taskFormKey,
        String assignedTo,
        String assignedBy,
        String title,
        String processId,
        LocalDateTime timestamp
) {
}
