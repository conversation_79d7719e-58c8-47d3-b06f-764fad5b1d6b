package co.com.gedsys.commons.config;

import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

import co.com.gedsys.commons.constant.amqp.ExchangeName;

@AutoConfiguration
@ConditionalOnClass({ TopicExchange.class, ConnectionFactory.class })
public class MainTopicExchangeConfig {

    @Bean(name = "mainTopicExchange")
    @ConditionalOnMissingBean(name = "mainTopicExchange")
    TopicExchange mainTopicExchange() {
        return new TopicExchange(ExchangeName.MAIN_TOPIC_EXCHANGE, true, false);
    }
}
